/**
 * 退出应用 - 跳转到应用信息页面进行强制停止
 * @param {string} 包名 - 应用包名，默认Brain Battle
 * @param {boolean} 自动强制停止 - 是否自动点击强制停止按钮，默认false
 * @returns {boolean} 是否成功跳转
 */
function 退出应用(包名, 自动强制停止) {
    try {
        // 跳转到应用信息页面
        var 跳转结果 = app.openAppSetting(包名 || "com.winrgames.brainbattle");

        if (!跳转结果) {
            console.error("跳转失败：应用可能未安装 - " + (包名 || "com.winrgames.brainbattle"));
            toast("应用未找到：" + (包名 || "com.winrgames.brainbattle"));
            return false;
        }

        console.log("成功跳转到应用信息页面：" + (包名 || "com.winrgames.brainbattle"));
        toast("已跳转到应用设置页面");

        // 可选的自动强制停止功能
        if (自动强制停止) {
            sleep(1000); // 等待页面加载

            // Brain Battle的强行停止按钮 - 遍历列表查找并点击强制停止按钮
            id("list").findOne().children().forEach(function(child) {
                var target = child.findOne(id("button2_negative"));
                if (target) {
                    target.click(); // 点击强制停止按钮
                    console.log("已点击Brain Battle强制停止按钮");
                }
            });

            sleep(500); // 延时0.5秒等待确认对话框

            // Brain Battle的强行停止确定按钮 - 点击确认按钮完成强制停止
            var 确认按钮 = id("button1").findOne();
            if (确认按钮) {
                确认按钮.click(); // 点击确认按钮
                console.log("已点击Brain Battle强制停止确认按钮");
                toast("Brain Battle应用已强制停止");
            } else {
                console.log("未找到确认按钮，请手动操作");
                toast("请手动点击确认按钮");
            }
        }

        return true;

    } catch (e) {
        console.error("退出应用函数执行失败：" + e.toString());
        toast("操作失败：" + e.message);
        return false;
    }
}

// // 导出函数供其他模块使用
// module.exports = {
//     退出应用: 退出应用
// };

// // 基础用法
// 退出应用();

// 自动强制停止
退出应用("com.winrgames.brainbattle", true);