# PromptX MCP服务器部署指南 (Augment专用)

## 📋 部署概述

PromptX是一个通过MCP协议为AI应用提供专业角色、记忆管理和知识体系的增强系统。本指南将帮助您在VS Code + Augment插件环境下完成部署。

**重要说明**: Augment有自己的MCP配置方式，与标准VS Code MCP配置不同。

## 🎯 功能特点

- **🎭 专业角色扮演**: 提供覆盖不同领域的专家角色
- **🧠 长期记忆与知识库**: AI能够记住关键信息和偏好
- **✨ AI角色创造工坊**: 2分钟内创建专业AI助手
- **🔌 轻松集成**: 通过Augment配置即可启用功能

## 🛠️ 核心工具

配置成功后，您将获得以下6个专业工具：

- `promptx_init`: 🏗️ **系统初始化** - 自动准备工作环境
- `promptx_hello`: 👋 **角色发现** - 浏览所有可用的专家角色
- `promptx_action`: ⚡ **角色激活** - 一键变身指定领域的专家
- `promptx_learn`: 📚 **知识学习** - 让AI学习特定的知识或技能
- `promptx_recall`: 🔍 **记忆检索** - 从记忆库中查找历史信息
- `promptx_remember`: 💾 **经验保存** - 将重要信息存入长期记忆

- 
第一：请用promptx_action指令: 激活关于AutoXjs游戏专家的身份

第二：请用promptx_recall指令记忆起Magic项目的最新开发进度和经验 ，编码习惯和规则经验，关键技术经验和解决方案




- 请你用： promptx_remember 与 promptx_learn 指令保存与学习当前magic项目的开发进度，与经验，知识，还有我的写代码习惯，规则等。

- 1.请你先全面熟读与学习README.md文档里面的项目规则，并且在开发过程中遵守此项目规则 

- 2.请你用： promptx_remember 指令保存与学习README.md文档里面的规则并且你要遵守里面的项目规则，在开发过程你必须要遵守此规则 

## 📋 Augment MCP部署步骤

### 方法一：使用Augment设置面板（推荐）

1. **打开Augment设置面板**
   - 在VS Code中打开Augment面板
   - 点击右上角的齿轮图标⚙️

2. **配置MCP服务器**
   - 在设置面板中找到"MCP servers"部分
   - 点击`+`按钮添加新服务器
   - 填写以下信息：
     - **Name**: `promptx`
     - **Command**: `npx -y -f --registry https://registry.npmjs.org dpml-prompt@beta mcp-server`

3. **保存配置**
   - 点击保存按钮
   - 重启VS Code

### 方法二：编辑settings.json文件

1. **打开Augment设置**
   - 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
   - 选择 "Edit Settings"
   - 在Advanced部分，点击 "Edit in settings.json"

2. **添加MCP服务器配置**
   在settings.json中添加以下配置：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "promptx",
        "command": "npx",
        "args": [
          "-y",
          "-f",
          "--registry",
          "https://registry.npmjs.org",
          "dpml-prompt@beta",
          "mcp-server"
        ]
      }
    ]
  }
}
```

3. **重启编辑器**
   - 保存settings.json文件
   - 重启VS Code以使配置生效

## 🚀 使用方式

### 在Augment Agent中使用

1. **打开Augment面板**
   - 在VS Code中打开Augment面板
   - 确保已安装并登录Augment

2. **使用Agent模式**
   - 在Augment面板中选择Agent模式
   - PromptX工具将自动可用

3. **查看可用工具**
   - 在Agent对话中，PromptX工具会根据需要自动调用
   - 您也可以直接请求使用特定工具

### 体验核心功能

#### 🎨 女娲角色创造工坊
```
输入: "我要女娲帮我创建一个角色"
功能: 2分钟内将想法变成专业AI助手
```

#### 👋 角色发现与激活
```
输入: "我要查看可用的专家角色"
使用: promptx_hello 发现角色
使用: promptx_action 激活角色
```

#### 🧠 记忆管理
```
保存记忆: promptx_remember
检索记忆: promptx_recall
```

### 验证配置

1. **检查MCP服务器状态**
   - 在Augment设置面板中查看MCP服务器列表
   - 确认PromptX服务器状态为运行中

2. **测试功能**
   - 在Agent中输入："请使用PromptX初始化系统"
   - 如果配置正确，应该能看到相关工具被调用

## ⚠️ 注意事项

### 项目状态
- PromptX目前处于**初始开发阶段**
- 可能遇到使用上的问题或不稳定情况
- 官方正在积极完善功能和修复问题

### 安全提醒
- MCP服务器可以运行任意代码
- 请确保来源可信
- 配置中已指定官方镜像源以确保安全性

### 性能优化
- 配置中使用了官方镜像源 `registry.npmjs.org`
- 如果安装缓慢，建议使用代理工具加速
- 避免切换到非官方镜像源

## 🆘 故障排除

### 常见问题

1. **MCP服务器未显示在Augment中**
   - 检查settings.json语法是否正确
   - 确认没有遗漏逗号或括号
   - 重启VS Code后再次检查

2. **服务器启动失败**
   - 检查网络连接
   - 确认Node.js和npm已安装
   - 在终端中手动运行命令测试：
     ```bash
     npx -y -f --registry https://registry.npmjs.org dpml-prompt@beta mcp-server
     ```

3. **工具不可用**
   - 确认Augment Agent模式已启用
   - 检查Augment设置面板中的MCP服务器状态
   - 重启VS Code和Augment插件

4. **配置冲突**
   - 如果之前配置过其他MCP服务器，确保配置格式一致
   - 避免在不同方法间混合配置（设置面板 vs settings.json）

### 调试步骤

1. **检查Augment日志**
   - 在Augment面板中查看错误信息
   - 检查VS Code开发者控制台

2. **验证依赖**
   - 确认Node.js版本 >= 18
   - 测试npm网络连接

3. **重置配置**
   - 如果遇到问题，可以删除MCP配置重新添加
   - 清除Augment缓存后重试

### 获取帮助

如果遇到问题，可以通过以下方式获取帮助：

- 🐛 **PromptX Issues**: https://github.com/Deepractice/PromptX/issues
- 💬 **开发者微信**: `deepracticex`
- 📧 **邮件支持**: `<EMAIL>`
- 🔧 **Augment支持**: https://support.augmentcode.com/

## 📚 相关资源

- **官方文档**: https://ozobiozobi.github.io/Autox_ozobi_Docs/doc/overview.html
- **项目地址**: https://github.com/Deepractice/PromptX
- **VS Code MCP文档**: https://code.visualstudio.com/docs/copilot/chat/mcp-servers

---

**部署完成时间**: 2025年1月
**配置版本**: v1.0
**适用环境**: VS Code + Augment插件
